{"name": "@types/mdurl", "version": "2.0.0", "description": "TypeScript definitions for mdurl", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mdurl", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "rokt33r", "url": "https://github.com/rokt33r"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./build/index.cjs.d.ts"}, "./*": {"import": "./*", "require": "./*"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mdurl"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "3dc3a8535d7207c6d737adfe2aac315c7f6546b2372bd343794994106ee251a5", "typeScriptVersion": "4.7"}