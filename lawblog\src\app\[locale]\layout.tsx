import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display, Noto_Sans_Ethiopic } from "next/font/google";
import "../globals.css";
import { Providers } from "@/components/providers";
import { Toaster } from "react-hot-toast";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales } from '@/i18n/request';

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const notoSansEthiopic = Noto_Sans_Ethiopic({
  subsets: ["ethiopic"],
  variable: "--font-ethiopic",
  display: "swap",
});

export const metadata: Metadata = {
  title: "አፈሳታ - Where Legal Minds Connect",
  description: "A cutting-edge blog platform for law students to share ideas, insights, and connect with the legal community.",
  keywords: ["law", "legal", "blog", "students", "education", "legal writing", "አፈሳታ"],
  authors: [{ name: "አፈሳታ Team" }],
  creator: "አፈሳታ",
  publisher: "አፈሳታ",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
    title: "አፈሳታ - Where Legal Minds Connect",
    description: "A cutting-edge blog platform for law students to share ideas, insights, and connect with the legal community.",
    siteName: "አፈሳታ",
  },
  twitter: {
    card: "summary_large_image",
    title: "አፈሳታ - Where Legal Minds Connect",
    description: "A cutting-edge blog platform for law students to share ideas, insights, and connect with the legal community.",
    creator: "@afersata",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale} className={`${inter.variable} ${playfair.variable} ${notoSansEthiopic.variable}`} dir={locale === 'am' ? 'ltr' : 'ltr'}>
      <body className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 font-inter antialiased">
        <NextIntlClientProvider messages={messages}>
          <Providers>
            {children}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: "#1f2937",
                  color: "#f9fafb",
                  borderRadius: "12px",
                  padding: "16px",
                  fontSize: "14px",
                  fontWeight: "500",
                },
              }}
            />
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
