'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  Scale, 
  Shield, 
  Building, 
  Leaf, 
  Globe, 
  Lightbulb, 
  Heart, 
  Users, 
  Flag,
  Laptop
} from 'lucide-react'

const categories = [
  {
    id: '1',
    name: 'Constitutional Law',
    slug: 'constitutional-law',
    description: 'Fundamental rights, government structure, and constitutional principles',
    color: '#DC2626',
    icon: Scale,
    postCount: 156
  },
  {
    id: '2',
    name: 'Criminal Law',
    slug: 'criminal-law',
    description: 'Criminal justice, procedure, and defense strategies',
    color: '#7C2D12',
    icon: Shield,
    postCount: 134
  },
  {
    id: '3',
    name: 'Corporate Law',
    slug: 'corporate-law',
    description: 'Business law, mergers, acquisitions, and corporate governance',
    color: '#1D4ED8',
    icon: Building,
    postCount: 98
  },
  {
    id: '4',
    name: 'Environmental Law',
    slug: 'environmental-law',
    description: 'Environmental regulations, climate law, and sustainability',
    color: '#059669',
    icon: Leaf,
    postCount: 87
  },
  {
    id: '5',
    name: 'International Law',
    slug: 'international-law',
    description: 'Treaties, international relations, and global legal frameworks',
    color: '#7C3AED',
    icon: Globe,
    postCount: 76
  },
  {
    id: '6',
    name: 'Intellectual Property',
    slug: 'intellectual-property',
    description: 'Patents, trademarks, copyrights, and IP protection',
    color: '#EA580C',
    icon: Lightbulb,
    postCount: 65
  },
  {
    id: '7',
    name: 'Family Law',
    slug: 'family-law',
    description: 'Divorce, custody, adoption, and domestic relations',
    color: '#BE185D',
    icon: Heart,
    postCount: 54
  },
  {
    id: '8',
    name: 'Labor Law',
    slug: 'labor-law',
    description: 'Employment rights, workplace regulations, and union law',
    color: '#0F766E',
    icon: Users,
    postCount: 43
  },
  {
    id: '9',
    name: 'Civil Rights',
    slug: 'civil-rights',
    description: 'Human rights, discrimination, and social justice',
    color: '#B91C1C',
    icon: Flag,
    postCount: 67
  },
  {
    id: '10',
    name: 'Legal Technology',
    slug: 'legal-technology',
    description: 'Legal tech innovations, AI in law, and digital transformation',
    color: '#4338CA',
    icon: Laptop,
    postCount: 32
  }
]

export function CategoriesSection() {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold font-playfair mb-6">
            Explore <span className="gradient-text">Categories</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Dive deep into specialized areas of law with expert insights and student perspectives.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Link href={`/categories/${category.slug}`}>
                <div className="relative glass rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden">
                  {/* Background Pattern */}
                  <div 
                    className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity"
                    style={{
                      background: `radial-gradient(circle at 50% 50%, ${category.color} 0%, transparent 70%)`
                    }}
                  />
                  
                  {/* Icon */}
                  <div className="relative z-10">
                    <div 
                      className="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 group-hover:scale-110 transition-transform"
                      style={{ backgroundColor: `${category.color}20` }}
                    >
                      <category.icon 
                        className="w-8 h-8" 
                        style={{ color: category.color }}
                      />
                    </div>

                    {/* Category Name */}
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {category.name}
                    </h3>

                    {/* Description */}
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {category.description}
                    </p>

                    {/* Post Count */}
                    <div className="flex items-center justify-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                      <span>{category.postCount}</span>
                      <span>articles</span>
                    </div>

                    {/* Hover Effect */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity rounded-2xl" />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* View All Categories Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/categories"
            className="inline-flex items-center space-x-2 glass font-semibold py-3 px-8 rounded-xl transition-all duration-300 hover:shadow-lg text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
          >
            <span>View All Categories</span>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
