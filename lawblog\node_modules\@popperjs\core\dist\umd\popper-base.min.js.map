{"version": 3, "file": "popper-base.min.js", "sources": ["../../src/dom-utils/getWindow.js", "../../src/dom-utils/instanceOf.js", "../../src/utils/math.js", "../../src/utils/userAgent.js", "../../src/dom-utils/isLayoutViewport.js", "../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/isTableElement.js", "../../src/dom-utils/getOffsetParent.js", "../../src/enums.js", "../../src/utils/orderModifiers.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/dom-utils/contains.js", "../../src/createPopper.js", "../../src/utils/debounce.js", "../../src/utils/mergeByName.js", "../../src/dom-utils/getLayoutRect.js", "../../src/utils/detectOverflow.js", "../../src/utils/expandToHashMap.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/computeOffsets.js", "../../src/utils/getBasePlacement.js", "../../src/utils/getVariation.js", "../../src/utils/getMainAxisFromPlacement.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const { defaultModifiers = [], defaultOptions = DEFAULT_OPTIONS } =\n    generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n"], "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "rect", "isElementScaled", "scroll", "offsets", "clientLeft", "clientTop", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "_element$ownerDocumen", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getContainingBlock", "basePlacements", "viewport", "popper", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "sort", "modifier", "add", "name", "requires", "requiresIfExists", "for<PERSON>ach", "dep", "has", "depModifier", "get", "push", "set", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "getViewportRect", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "direction", "getDocumentRect", "getClippingParents", "clippingParents", "clipperElement", "parent", "child", "rootNode", "getRootNode", "contains", "next", "isSameNode", "DEFAULT_OPTIONS", "placement", "areValidElements", "args", "some", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "reference", "options", "fn", "pending", "state", "orderedModifiers", "modifiersData", "elements", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "contextElement", "merged", "reduce", "acc", "phase", "orderModifiers", "current", "existing", "data", "Object", "keys", "key", "m", "enabled", "effect", "cleanupFn", "noopFn", "update", "forceUpdate", "rects", "abs", "reset", "index", "length", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "createPopper", "value", "boundary", "rootBoundary", "elementContext", "altBoundary", "padding", "paddingObject", "mergePaddingObject", "hashMap", "altContext", "popperRect", "clippingClientRect", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "referenceClientRect", "popperOffsets", "basePlacement", "split", "getBasePlacement", "variation", "getVariation", "commonX", "commonY", "mainAxis", "getMainAxisFromPlacement", "len", "computeOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "offset", "multiply", "axis"], "mappings": ";;;;8OAIe,SAASA,EAAUC,MACpB,MAARA,SACKC,UAGe,oBAApBD,EAAKE,WAAkC,KACnCC,EAAgBH,EAAKG,qBACpBA,GAAgBA,EAAcC,aAAwBH,cAGxDD,ECTT,SAASK,EAAUL,UAEVA,aADYD,EAAUC,GAAMM,SACEN,aAAgBM,QAKvD,SAASC,EAAcP,UAEdA,aADYD,EAAUC,GAAMQ,aACER,aAAgBQ,YAKvD,SAASC,EAAaT,SAEM,oBAAfU,aAIJV,aADYD,EAAUC,GAAMU,YACEV,aAAgBU,YCxBhD,IAAMC,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAQF,KAAKE,MCMX,SAASC,QAChBC,EAAUC,UAAsBC,2BAElCF,GAAAA,EAAQG,QAAUC,MAAMC,QAAQL,EAAOG,QAClCH,EAAOG,OACXG,KAAI,SAACC,UAAYA,EAAKC,UAASD,EAAKE,WACpCC,KAAK,KAGHT,UAAUU,UCfJ,SAASC,WACd,iCAAiCC,KAAKd,KCGjC,SAASe,EACtBC,EACAC,EACAC,YADAD,IAAAA,GAAwB,YACxBC,IAAAA,GAA2B,OAErBC,EAAaH,EAAQD,wBACvBK,EAAS,EACTC,EAAS,EAETJ,GAAgBzB,EAAcwB,KAChCI,EACGJ,EAAsBM,YAAc,GACjCvB,EAAMoB,EAAWI,OAAUP,EAAsBM,aACjD,EACND,EACGL,EAAsBQ,aAAe,GAClCzB,EAAMoB,EAAWM,QAAWT,EAAsBQ,cAClD,OAGAE,GAAmBpC,EAAU0B,GAAWhC,EAAUgC,GAAW9B,QAA7DwC,eACFC,GAAoBd,KAAsBK,EAE1CU,GACHT,EAAWU,MACTF,GAAoBD,EAAiBA,EAAeI,WAAa,IACpEV,EACIW,GACHZ,EAAWa,KACTL,GAAoBD,EAAiBA,EAAeO,UAAY,IACnEZ,EACIE,EAAQJ,EAAWI,MAAQH,EAC3BK,EAASN,EAAWM,OAASJ,QAE5B,CACLE,MAAAA,EACAE,OAAAA,EACAO,IAAKD,EACLG,MAAON,EAAIL,EACXY,OAAQJ,EAAIN,EACZI,KAAMD,EACNA,EAAAA,EACAG,EAAAA,GC7CW,SAASK,EAAgBnD,OAChCoD,EAAMrD,EAAUC,SAIf,CACLqD,WAJiBD,EAAIE,YAKrBC,UAJgBH,EAAII,aCJT,SAASC,EAAY1B,UAC3BA,GAAWA,EAAQ2B,UAAY,IAAIC,cAAgB,KCA7C,SAASC,EACtB7B,WAIG1B,EAAU0B,GACPA,EAAQ5B,cAER4B,EAAQ8B,WAAa5D,OAAO4D,UAChCC,gBCRW,SAASC,EAAoBhC,UASxCD,EAAsB8B,EAAmB7B,IAAUa,KACnDO,EAAgBpB,GAASsB,WCZd,SAASW,EACtBjC,UAEOhC,EAAUgC,GAASiC,iBAAiBjC,GCH9B,SAASkC,EAAelC,SAEMiC,EAAiBjC,GAApDmC,IAAAA,SAAUC,IAAAA,UAAWC,IAAAA,gBACtB,6BAA6BvC,KAAKqC,EAAWE,EAAYD,GCenD,SAASE,EACtBC,EACAC,EACAC,YAAAA,IAAAA,GAAmB,OCjBiBxE,ECLO+B,EFwBrC0C,EAA0BlE,EAAcgE,GACxCG,EACJnE,EAAcgE,IAjBlB,SAAyBxC,OACjB4C,EAAO5C,EAAQD,wBACfK,EAASrB,EAAM6D,EAAKrC,OAASP,EAAQM,aAAe,EACpDD,EAAStB,EAAM6D,EAAKnC,QAAUT,EAAQQ,cAAgB,SAE1C,IAAXJ,GAA2B,IAAXC,EAYUwC,CAAgBL,GAC3CT,EAAkBF,EAAmBW,GACrCI,EAAO7C,EACXwC,EACAI,EACAF,GAGEK,EAAS,CAAExB,WAAY,EAAGE,UAAW,GACrCuB,EAAU,CAAEnC,EAAG,EAAGG,EAAG,UAErB2B,IAA6BA,IAA4BD,MAE3B,SAA9Bf,EAAYc,IAEZN,EAAeH,MAEfe,GCtCgC7E,EDsCTuE,KCrCdxE,EAAUC,IAAUO,EAAcP,GCLxC,CACLqD,YAFyCtB,EDSb/B,GCPRqD,WACpBE,UAAWxB,EAAQwB,WDIZJ,EAAgBnD,IDuCnBO,EAAcgE,KAChBO,EAAUhD,EAAsByC,GAAc,IACtC5B,GAAK4B,EAAaQ,WAC1BD,EAAQhC,GAAKyB,EAAaS,WACjBlB,IACTgB,EAAQnC,EAAIoB,EAAoBD,KAI7B,CACLnB,EAAGgC,EAAK/B,KAAOiC,EAAOxB,WAAayB,EAAQnC,EAC3CG,EAAG6B,EAAK5B,IAAM8B,EAAOtB,UAAYuB,EAAQhC,EACzCR,MAAOqC,EAAKrC,MACZE,OAAQmC,EAAKnC,QGxDF,SAASyC,EAAclD,SACP,SAAzB0B,EAAY1B,GACPA,EAOPA,EAAQmD,cACRnD,EAAQoD,aACP1E,EAAasB,GAAWA,EAAQqD,KAAO,OAExCxB,EAAmB7B,GCZR,SAASsD,EAAgBrF,SAClC,CAAC,OAAQ,OAAQ,aAAasF,QAAQ7B,EAAYzD,KAAU,EAEvDA,EAAKG,cAAcoF,KAGxBhF,EAAcP,IAASiE,EAAejE,GACjCA,EAGFqF,EAAgBJ,EAAcjF,ICHxB,SAASwF,EACtBzD,EACA0D,kBAAAA,IAAAA,EAAgC,QAE1BC,EAAeL,EAAgBtD,GAC/B4D,EAASD,cAAiB3D,EAAQ5B,sBAARyF,EAAuBL,MACjDnC,EAAMrD,EAAU2F,GAChBG,EAASF,EACX,CAACvC,GAAK0C,OACJ1C,EAAIX,gBAAkB,GACtBwB,EAAeyB,GAAgBA,EAAe,IAEhDA,EACEK,EAAcN,EAAKK,OAAOD,UAEzBF,EACHI,EAEAA,EAAYD,OAAON,EAAkBP,EAAcY,KC5B1C,SAASG,EAAejE,SAC9B,CAAC,QAAS,KAAM,MAAMuD,QAAQ7B,EAAY1B,KAAa,ECKhE,SAASkE,EAAoBlE,UAExBxB,EAAcwB,IAEwB,UAAvCiC,EAAiBjC,GAASmE,SAKrBnE,EAAQwC,aAHN,KAsDI,SAAS4B,EAAgBpE,WAChC9B,EAASF,EAAUgC,GAErBwC,EAAe0B,EAAoBlE,GAGrCwC,GACAyB,EAAezB,IAC6B,WAA5CP,EAAiBO,GAAc2B,UAE/B3B,EAAe0B,EAAoB1B,UAInCA,IAC+B,SAA9Bd,EAAYc,IACoB,SAA9Bd,EAAYc,IACiC,WAA5CP,EAAiBO,GAAc2B,UAE5BjG,EAGFsE,GApET,SAA4BxC,OACpBqE,EAAY,WAAWvE,KAAKd,QACrB,WAAWc,KAAKd,MAEjBR,EAAcwB,IAGI,UADTiC,EAAiBjC,GACrBmE,gBACN,SAIPG,EAAcpB,EAAclD,OAE5BtB,EAAa4F,KACfA,EAAcA,EAAYjB,MAI1B7E,EAAc8F,IACd,CAAC,OAAQ,QAAQf,QAAQ7B,EAAY4C,IAAgB,GACrD,KACMC,EAAMtC,EAAiBqC,MAMT,SAAlBC,EAAIC,WACgB,SAApBD,EAAIE,aACY,UAAhBF,EAAIG,UACsD,IAA1D,CAAC,YAAa,eAAenB,QAAQgB,EAAII,aACxCN,GAAgC,WAAnBE,EAAII,YACjBN,GAAaE,EAAIK,QAAyB,SAAfL,EAAIK,cAEzBN,EAEPA,EAAcA,EAAYlB,kBAIvB,KA2BgByB,CAAmB7E,IAAY9B,EC1FjD,IAAM8C,EAAa,MACbG,EAAmB,SACnBD,EAAiB,QACjBL,EAAe,OAOfiE,EAAuC,CAAC9D,EAAKG,EAAQD,EAAOL,GAO5DkE,EAAuB,WAIvBC,EAAmB,SA+CnBC,EAAwC,CAXb,aACZ,OACU,YAEE,aACZ,OACU,YAEI,cACZ,QACU,cC/DxC,SAASC,EAAMC,OACP5F,EAAM,IAAI6F,IACVC,EAAU,IAAIC,IACdC,EAAS,YAONC,EAAKC,GACZJ,EAAQK,IAAID,EAASE,gBAGfF,EAASG,UAAY,GACrBH,EAASI,kBAAoB,IAG1BC,SAAQ,SAAAC,OACVV,EAAQW,IAAID,GAAM,KACfE,EAAc1G,EAAI2G,IAAIH,GAExBE,GACFT,EAAKS,OAKXV,EAAOY,KAAKV,UAvBdN,EAAUW,SAAQ,SAAAL,GAChBlG,EAAI6G,IAAIX,EAASE,KAAMF,MAyBzBN,EAAUW,SAAQ,SAAAL,GACXJ,EAAQW,IAAIP,EAASE,OAExBH,EAAKC,MAIFF,ECxCM,SAASc,EAAiBzD,2BAElCA,GACH/B,KAAM+B,EAAKhC,EACXI,IAAK4B,EAAK7B,EACVG,MAAO0B,EAAKhC,EAAIgC,EAAKrC,MACrBY,OAAQyB,EAAK7B,EAAI6B,EAAKnC,SC2B1B,SAAS6F,EACPtG,EACAuG,EACAC,UAEOD,IAAmBxB,EACtBsB,ECnCS,SACbrG,EACAwG,OAEMnF,EAAMrD,EAAUgC,GAChByG,EAAO5E,EAAmB7B,GAC1BU,EAAiBW,EAAIX,eAEvBH,EAAQkG,EAAKC,YACbjG,EAASgG,EAAKE,aACd/F,EAAI,EACJG,EAAI,KAEJL,EAAgB,CAClBH,EAAQG,EAAeH,MACvBE,EAASC,EAAeD,WAElBmG,EAAiB/G,KAEnB+G,IAAoBA,GAA+B,UAAbJ,KACxC5F,EAAIF,EAAeI,WACnBC,EAAIL,EAAeO,iBAIhB,CACLV,MAAAA,EACAE,OAAAA,EACAG,EAAGA,EAAIoB,EAAoBhC,GAC3Be,EAAAA,GDMmB8F,CAAgB7G,EAASwG,IAC1ClI,EAAUiI,GAzBhB,SACEvG,EACAwG,OAEM5D,EAAO7C,EAAsBC,GAAS,EAAoB,UAAbwG,UAEnD5D,EAAK5B,IAAM4B,EAAK5B,IAAMhB,EAAQiD,UAC9BL,EAAK/B,KAAO+B,EAAK/B,KAAOb,EAAQgD,WAChCJ,EAAKzB,OAASyB,EAAK5B,IAAMhB,EAAQ2G,aACjC/D,EAAK1B,MAAQ0B,EAAK/B,KAAOb,EAAQ0G,YACjC9D,EAAKrC,MAAQP,EAAQ0G,YACrB9D,EAAKnC,OAAST,EAAQ2G,aACtB/D,EAAKhC,EAAIgC,EAAK/B,KACd+B,EAAK7B,EAAI6B,EAAK5B,IAEP4B,EAWHkE,CAA2BP,EAAgBC,GAC3CH,EEnCS,SAAyBrG,SAChCyG,EAAO5E,EAAmB7B,GAC1B+G,EAAY3F,EAAgBpB,GAC5BwD,WAAOxD,EAAQ5B,sBAARyF,EAAuBL,KAE9BjD,EAAQ3B,EACZ6H,EAAKO,YACLP,EAAKC,YACLlD,EAAOA,EAAKwD,YAAc,EAC1BxD,EAAOA,EAAKkD,YAAc,GAEtBjG,EAAS7B,EACb6H,EAAKQ,aACLR,EAAKE,aACLnD,EAAOA,EAAKyD,aAAe,EAC3BzD,EAAOA,EAAKmD,aAAe,GAGzB/F,GAAKmG,EAAUzF,WAAaU,EAAoBhC,GAC9Ce,GAAKgG,EAAUvF,gBAE4B,QAA7CS,EAAiBuB,GAAQiD,GAAMS,YACjCtG,GAAKhC,EAAI6H,EAAKC,YAAalD,EAAOA,EAAKkD,YAAc,GAAKnG,GAGrD,CAAEA,MAAAA,EAAOE,OAAAA,EAAQG,EAAAA,EAAGG,EAAAA,GFUNoG,CAAgBtF,EAAmB7B,KAM1D,SAASoH,EAAmBpH,OACpBqH,EAAkB5D,EAAkBP,EAAclD,IAGlDsH,EADJ,CAAC,WAAY,SAAS/D,QAAQtB,EAAiBjC,GAASmE,WAAa,GAEhD3F,EAAcwB,GAC/BoE,EAAgBpE,GAChBA,SAED1B,EAAUgJ,GAKRD,EAAgBzC,QACrB,SAAC2B,UACCjI,EAAUiI,IGhED,SAAkBgB,EAAiBC,OAC1CC,EAAWD,EAAME,aAAeF,EAAME,iBAGxCH,EAAOI,SAASH,UACX,EAGJ,GAAIC,GAAY/I,EAAa+I,GAAW,KACvCG,EAAOJ,IACR,IACGI,GAAQL,EAAOM,WAAWD,UACrB,EAGTA,EAAOA,EAAKxE,YAAcwE,EAAKvE,WACxBuE,UAIJ,EH6CHD,CAASpB,EAAgBe,IACO,SAAhC5F,EAAY6E,MARP,GI3CX,IAAMuB,EAAuC,CAC3CC,UAAW,SACX5C,UAAW,GACXqB,SAAU,YAQZ,SAASwB,+BAAoBC,2BAAAA,yBACnBA,EAAKC,MACX,SAAClI,WACGA,GAAoD,mBAAlCA,EAAQD,0BAI3B,SAASoI,EAAgBC,YAAAA,IAAAA,EAAwC,UAEpEA,MADMC,iBAAAA,aAAmB,SAAIC,eAAAA,aAAiBR,WAGzC,SACLS,EACAvD,EACAwD,YAAAA,IAAAA,EAA6CF,OCzCbG,EAC9BC,ED0CEC,EAAuB,CACzBZ,UAAW,SACXa,iBAAkB,GAClBJ,yBAAcV,EAAoBQ,GAClCO,cAAe,GACfC,SAAU,CACRP,UAAAA,EACAvD,OAAAA,GAEF+D,WAAY,GACZC,OAAQ,IAGNC,EAAsC,GACtCC,GAAc,EAEZC,EAAW,CACfR,MAAAA,EACAS,oBAAWC,OACHb,EACwB,mBAArBa,EACHA,EAAiBV,EAAMH,SACvBa,EAENC,IAEAX,EAAMH,yBAEDF,EACAK,EAAMH,QACNA,GAGLG,EAAMY,cAAgB,CACpBhB,UAAWjK,EAAUiK,GACjB9E,EAAkB8E,GAClBA,EAAUiB,eACV/F,EAAkB8E,EAAUiB,gBAC5B,GACJxE,OAAQvB,EAAkBuB,QEhFlCG,EAEMsE,EFmFMb,EN3CC,SACbzD,OAGMyD,EAAmB1D,EAAMC,UAGxBF,EAAeyE,QAAO,SAACC,EAAKC,UAC1BD,EAAI5F,OACT6E,EAAiBhE,QAAO,SAAAa,UAAYA,EAASmE,QAAUA,QAExD,IMgC4BC,EErF/B1E,YFsFwBkD,EAAqBM,EAAMH,QAAQrD,WEpFrDsE,EAAStE,EAAUuE,QAAO,SAACD,EAAQK,OACjCC,EAAWN,EAAOK,EAAQnE,aAChC8D,EAAOK,EAAQnE,MAAQoE,mBAEdA,EACAD,GACHtB,yBAAcuB,EAASvB,QAAYsB,EAAQtB,SAC3CwB,sBAAWD,EAASC,KAASF,EAAQE,QAEvCF,EACGL,IACN,IAGIQ,OAAOC,KAAKT,GAAQlK,KAAI,SAAA4K,UAAOV,EAAOU,eF0EvCxB,EAAMC,iBAAmBA,EAAiBhE,QAAO,SAACwF,UAAMA,EAAEC,WAsG5D1B,EAAMC,iBAAiB9C,SAAQ,gBAAGH,IAAAA,SAAM6C,QAAAA,aAAU,KAAI8B,IAAAA,UAC9B,mBAAXA,EAAuB,KAC1BC,EAAYD,EAAO,CAAE3B,MAAAA,EAAOhD,KAAAA,EAAMwD,SAAAA,EAAUX,QAAAA,IAC5CgC,EAAS,aACfvB,EAAiB9C,KAAKoE,GAAaC,OAtG9BrB,EAASsB,UAQlBC,2BACMxB,SAI0BP,EAAMG,SAA5BP,IAAAA,UAAWvD,IAAAA,UAIdgD,EAAiBO,EAAWvD,IG7G1B,IAAuBhF,EAC9BG,EAIFI,EACAE,EH4GEkI,EAAMgC,MAAQ,CACZpC,UAAWjG,EACTiG,EACAnE,EAAgBY,GACW,UAA3B2D,EAAMH,QAAQhC,UAEhBxB,QGxH4BhF,EHwHNgF,EGvHxB7E,EAAaJ,EAAsBC,GAIrCO,EAAQP,EAAQM,YAChBG,EAAST,EAAQQ,aAEjB3B,KAAK+L,IAAIzK,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjB1B,KAAK+L,IAAIzK,EAAWM,OAASA,IAAW,IAC1CA,EAASN,EAAWM,QAGf,CACLG,EAAGZ,EAAQc,WACXC,EAAGf,EAAQiB,UACXV,MAAAA,EACAE,OAAAA,KH4GIkI,EAAMkC,OAAQ,EAEdlC,EAAMZ,UAAYY,EAAMH,QAAQT,UAMhCY,EAAMC,iBAAiB9C,SACrB,SAACL,UACEkD,EAAME,cAAcpD,EAASE,uBACzBF,EAASuE,aAIb,IAAIc,EAAQ,EAAGA,EAAQnC,EAAMC,iBAAiBmC,OAAQD,QACrC,IAAhBnC,EAAMkC,aAMyBlC,EAAMC,iBAAiBkC,GAAlDrC,IAAAA,OAAID,QAAAA,aAAU,KAAI7C,IAAAA,KAER,mBAAP8C,IACTE,EAAQF,EAAG,CAAEE,MAAAA,EAAOH,QAAAA,EAAS7C,KAAAA,EAAMwD,SAAAA,KAAeR,QARlDA,EAAMkC,OAAQ,EACdC,GAAS,KAcfL,QCpK8BhC,EDqK5B,kBACE,IAAIuC,SAAuB,SAACC,GAC1B9B,EAASuB,cACTO,EAAQtC,OCtKX,kBACAD,IACHA,EAAU,IAAIsC,SAAW,SAAAC,GACvBD,QAAQC,UAAUC,MAAK,WACrBxC,OAAUyC,EACVF,EAAQxC,YAKPC,IDgKL0C,mBACE9B,IACAJ,GAAc,QAIblB,EAAiBO,EAAWvD,UACxBmE,WAwBAG,IACPL,EAAiBnD,SAAQ,SAAC2C,UAAOA,OACjCQ,EAAmB,UAvBrBE,EAASC,WAAWZ,GAAS0C,MAAK,SAACvC,IAC5BO,GAAeV,EAAQ6C,eAC1B7C,EAAQ6C,cAAc1C,MAwBnBQ,OAIEmC,EAAenD,sCIrLb,SACbQ,EACAH,YAAAA,IAAAA,EAA2B,QC9B3B+C,IDwCI/C,MAPFT,UAAAA,aAAYY,EAAMZ,gBAClBvB,SAAAA,aAAWmC,EAAMnC,eACjBgF,SAAAA,aXvB8C,wBWwB9CC,aAAAA,aAAe1G,QACf2G,eAAAA,aAAiB1G,QACjB2G,YAAAA,oBACAC,QAAAA,aAAU,IAGNC,EE3CO,SACbA,2BCDO,CACL7K,IAAK,EACLE,MAAO,EACPC,OAAQ,EACRN,KAAM,GDCHgL,GFsCiBC,CACD,iBAAZF,EACHA,GC5CNL,ED6CsBK,EAAS9G,EC5CnB4E,QAAO,SAACqC,EAAS5B,UAC3B4B,EAAQ5B,GAAOoB,EACRQ,IACN,MD4CGC,EAAaN,IAAmB1G,EX9BF,YW8BuBA,EAErDiH,EAAatD,EAAMgC,MAAM3F,OACzBhF,EAAU2I,EAAMG,SAAS6C,EAAcK,EAAaN,GAEpDQ,ERiBO,SACblM,EACAwL,EACAC,EACAjF,OAEM2F,EACS,oBAAbX,EACIpE,EAAmBpH,GACnB,GAAG+D,OAAOyH,GACVnE,YAAsB8E,GAAqBV,IAC3CW,EAAsB/E,EAAgB,GAEtCgF,EAAehF,EAAgBqC,QAAO,SAAC4C,EAAS/F,OAC9C3D,EAAO0D,EAA2BtG,EAASuG,EAAgBC,UAEjE8F,EAAQtL,IAAMpC,EAAIgE,EAAK5B,IAAKsL,EAAQtL,KACpCsL,EAAQpL,MAAQpC,EAAI8D,EAAK1B,MAAOoL,EAAQpL,OACxCoL,EAAQnL,OAASrC,EAAI8D,EAAKzB,OAAQmL,EAAQnL,QAC1CmL,EAAQzL,KAAOjC,EAAIgE,EAAK/B,KAAMyL,EAAQzL,MAE/ByL,IACNhG,EAA2BtG,EAASoM,EAAqB5F,WAE5D6F,EAAa9L,MAAQ8L,EAAanL,MAAQmL,EAAaxL,KACvDwL,EAAa5L,OAAS4L,EAAalL,OAASkL,EAAarL,IACzDqL,EAAazL,EAAIyL,EAAaxL,KAC9BwL,EAAatL,EAAIsL,EAAarL,IAEvBqL,EQ9CoBE,CACzBjO,EAAU0B,GACNA,EACAA,EAAQwJ,gBAAkB3H,EAAmB8G,EAAMG,SAAS9D,QAChEwG,EACAC,EACAjF,GAGIgG,EAAsBzM,EAAsB4I,EAAMG,SAASP,WAE3DkE,EIzDO,gBAeT1J,EAdJwF,IAAAA,UACAvI,IAAAA,QACA+H,IAAAA,UAOM2E,EAAgB3E,ECnBT,SACbA,UAEQA,EAAU4E,MAAM,KAAK,GDgBKC,CAAiB7E,GAAa,KAC1D8E,EAAY9E,EEpBL,SAAsBA,UAC3BA,EAAU4E,MAAM,KAAK,GFmBCG,CAAa/E,GAAa,KAClDgF,EAAUxE,EAAU3H,EAAI2H,EAAUhI,MAAQ,EAAIP,EAAQO,MAAQ,EAC9DyM,EAAUzE,EAAUxH,EAAIwH,EAAU9H,OAAS,EAAIT,EAAQS,OAAS,SAG9DiM,QACD1L,EACH+B,EAAU,CACRnC,EAAGmM,EACHhM,EAAGwH,EAAUxH,EAAIf,EAAQS,mBAGxBU,EACH4B,EAAU,CACRnC,EAAGmM,EACHhM,EAAGwH,EAAUxH,EAAIwH,EAAU9H,mBAG1BS,EACH6B,EAAU,CACRnC,EAAG2H,EAAU3H,EAAI2H,EAAUhI,MAC3BQ,EAAGiM,cAGFnM,EACHkC,EAAU,CACRnC,EAAG2H,EAAU3H,EAAIZ,EAAQO,MACzBQ,EAAGiM,iBAILjK,EAAU,CACRnC,EAAG2H,EAAU3H,EACbG,EAAGwH,EAAUxH,OAIbkM,EAAWP,EGzDJ,SACb3E,SAEO,CAAC,MAAO,UAAUxE,QAAQwE,IAAc,EAAI,IAAM,IHuDrDmF,CAAyBR,GACzB,QAEY,MAAZO,EAAkB,KACdE,EAAmB,MAAbF,EAAmB,SAAW,eAElCJ,OftDkB,QewDtB9J,EAAQkK,GACNlK,EAAQkK,IAAa1E,EAAU4E,GAAO,EAAInN,EAAQmN,GAAO,afxDzC,Me2DlBpK,EAAQkK,GACNlK,EAAQkK,IAAa1E,EAAU4E,GAAO,EAAInN,EAAQmN,GAAO,WAM1DpK,EJXeqK,CAAe,CACnC7E,UAAWiE,EACXxM,QAASiM,EACTzF,SAAU,WACVuB,UAAAA,IAGIsF,EAAmBhH,mBACpB4F,EACAQ,IAGCa,EACJ5B,IAAmB1G,EAASqI,EAAmBb,EAI3Ce,EAAkB,CACtBvM,IAAKkL,EAAmBlL,IAAMsM,EAAkBtM,IAAM6K,EAAc7K,IACpEG,OACEmM,EAAkBnM,OAClB+K,EAAmB/K,OACnB0K,EAAc1K,OAChBN,KAAMqL,EAAmBrL,KAAOyM,EAAkBzM,KAAOgL,EAAchL,KACvEK,MACEoM,EAAkBpM,MAAQgL,EAAmBhL,MAAQ2K,EAAc3K,OAGjEsM,EAAa7E,EAAME,cAAc4E,UAGnC/B,IAAmB1G,GAAUwI,EAAY,KACrCC,EAASD,EAAWzF,GAE1BkC,OAAOC,KAAKqD,GAAiBzH,SAAQ,SAACqE,OAC9BuD,EAAW,CAACxM,EAAOC,GAAQoC,QAAQ4G,IAAQ,EAAI,GAAK,EACpDwD,EAAO,CAAC3M,EAAKG,GAAQoC,QAAQ4G,IAAQ,EAAI,IAAM,IACrDoD,EAAgBpD,IAAQsD,EAAOE,GAAQD,YAIpCH"}