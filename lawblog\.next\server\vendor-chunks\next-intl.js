"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProviderServer)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getConfigNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\");\n/* harmony import */ var _server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/react-server/getFormats.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\");\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\");\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\");\n\n\n\n\n\n\n\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await (0,_server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])() : formats,\n    locale: locale ?? (await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])()),\n    messages: messages === undefined ? await (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await (0,_server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()),\n    timeZone: timeZone ?? (await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()),\n    ...rest\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ getRequestLocale)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\n\nasync function getHeadersImpl() {\n  const promiseOrValue = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n\n  // Compatibility with Next.js <15\n  return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || (await getLocaleFromHeader());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ getCachedRequestLocale),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ setCachedRequestLocale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNENBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXFJlcXVlc3RMb2NhbGVDYWNoZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcblxuLy8gU2VlIGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qcy9kaXNjdXNzaW9ucy81ODg2MlxuZnVuY3Rpb24gZ2V0Q2FjaGVJbXBsKCkge1xuICBjb25zdCB2YWx1ZSA9IHtcbiAgICBsb2NhbGU6IHVuZGVmaW5lZFxuICB9O1xuICByZXR1cm4gdmFsdWU7XG59XG5jb25zdCBnZXRDYWNoZSA9IGNhY2hlKGdldENhY2hlSW1wbCk7XG5mdW5jdGlvbiBnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlKCkge1xuICByZXR1cm4gZ2V0Q2FjaGUoKS5sb2NhbGU7XG59XG5mdW5jdGlvbiBzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlKGxvY2FsZSkge1xuICBnZXRDYWNoZSgpLmxvY2FsZSA9IGxvY2FsZTtcbn1cblxuZXhwb3J0IHsgZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSwgc2V0Q2FjaGVkUmVxdWVzdExvY2FsZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./src/i18n/request.ts\");\n/* harmony import */ var _validateLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\");\n\n\n\n\n\n\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__.getRequestLocale)();\n    }\n  };\n  let result = getConfig(params);\n  if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    (0,_validateLocale_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(receiveRuntimeConfigImpl);\nconst getFormatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.b);\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.d);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(next_intl_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"], localeOverride);\n  return {\n    ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.i)(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfigNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getConfigNowImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.now;\n}\nconst getConfigNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigNowImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EscUJBQXFCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0Q29uZmlnTm93LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldENvbmZpZ05vd0ltcGwobG9jYWxlKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gY29uZmlnLm5vdztcbn1cbmNvbnN0IGdldENvbmZpZ05vdyA9IGNhY2hlKGdldENvbmZpZ05vd0ltcGwpO1xuXG5leHBvcnQgeyBnZXRDb25maWdOb3cgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getFormatsCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.formats;\n}\nconst getFormats = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatsCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDUzs7QUFFdkM7QUFDQSx1QkFBdUIseURBQVM7QUFDaEM7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBSzs7QUFFUyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldEZvcm1hdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuYXN5bmMgZnVuY3Rpb24gZ2V0Rm9ybWF0c0NhY2hlZEltcGwoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmZvcm1hdHM7XG59XG5jb25zdCBnZXRGb3JtYXRzID0gY2FjaGUoZ2V0Rm9ybWF0c0NhY2hlZEltcGwpO1xuXG5leHBvcnQgeyBnZXRGb3JtYXRzIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLocaleCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getLocaleCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.locale;\n}\nconst getLocaleCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getLocaleCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0Esd0JBQXdCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0TG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldExvY2FsZUNhY2hlZEltcGwoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmxvY2FsZTtcbn1cbmNvbnN0IGdldExvY2FsZUNhY2hlZCA9IGNhY2hlKGdldExvY2FsZUNhY2hlZEltcGwpO1xuXG5leHBvcnQgeyBnZXRMb2NhbGVDYWNoZWQgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMessages),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ getMessagesFromConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EsMEJBQTBCLDRDQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFeUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcS3JhaW1hdGljXFxEZXNrdG9wXFxhZmVyc2F0YVxcbGF3YmxvZ1xcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRNZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5mdW5jdGlvbiBnZXRNZXNzYWdlc0Zyb21Db25maWcoY29uZmlnKSB7XG4gIGlmICghY29uZmlnLm1lc3NhZ2VzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdObyBtZXNzYWdlcyBmb3VuZC4gSGF2ZSB5b3UgY29uZmlndXJlZCB0aGVtIGNvcnJlY3RseT8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbWVzc2FnZXMnKTtcbiAgfVxuICByZXR1cm4gY29uZmlnLm1lc3NhZ2VzO1xufVxuYXN5bmMgZnVuY3Rpb24gZ2V0TWVzc2FnZXNDYWNoZWRJbXBsKGxvY2FsZSkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGdldE1lc3NhZ2VzRnJvbUNvbmZpZyhjb25maWcpO1xufVxuY29uc3QgZ2V0TWVzc2FnZXNDYWNoZWQgPSBjYWNoZShnZXRNZXNzYWdlc0NhY2hlZEltcGwpO1xuYXN5bmMgZnVuY3Rpb24gZ2V0TWVzc2FnZXMob3B0cykge1xuICByZXR1cm4gZ2V0TWVzc2FnZXNDYWNoZWQob3B0cz8ubG9jYWxlKTtcbn1cblxuZXhwb3J0IHsgZ2V0TWVzc2FnZXMgYXMgZGVmYXVsdCwgZ2V0TWVzc2FnZXNGcm9tQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getRequestConfig)\n/* harmony export */ });\n/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldFJlcXVlc3RDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTaG91bGQgYmUgY2FsbGVkIGluIGBpMThuL3JlcXVlc3QudHNgIHRvIGNyZWF0ZSB0aGUgY29uZmlndXJhdGlvbiBmb3IgdGhlIGN1cnJlbnQgcmVxdWVzdC5cbiAqL1xuZnVuY3Rpb24gZ2V0UmVxdWVzdENvbmZpZyhjcmVhdGVSZXF1ZXN0Q29uZmlnKSB7XG4gIHJldHVybiBjcmVhdGVSZXF1ZXN0Q29uZmlnO1xufVxuXG5leHBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeZone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ1M7O0FBRXZDO0FBQ0EsdUJBQXVCLHlEQUFTO0FBQ2hDO0FBQ0E7QUFDQSwwQkFBMEIsNENBQUs7QUFDL0I7QUFDQTtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldFRpbWVab25lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldFRpbWVab25lQ2FjaGVkSW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBjb25maWcudGltZVpvbmU7XG59XG5jb25zdCBnZXRUaW1lWm9uZUNhY2hlZCA9IGNhY2hlKGdldFRpbWVab25lQ2FjaGVkSW1wbCk7XG5hc3luYyBmdW5jdGlvbiBnZXRUaW1lWm9uZShvcHRzKSB7XG4gIHJldHVybiBnZXRUaW1lWm9uZUNhY2hlZChvcHRzPy5sb2NhbGUpO1xufVxuXG5leHBvcnQgeyBnZXRUaW1lWm9uZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ validateLocale)\n/* harmony export */ });\nfunction validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixzREFBc0QsT0FBTztBQUM3RDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXHZhbGlkYXRlTG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHZhbGlkYXRlTG9jYWxlKGxvY2FsZSkge1xuICB0cnkge1xuICAgIGNvbnN0IGNvbnN0cnVjdGVkID0gbmV3IEludGwuTG9jYWxlKGxvY2FsZSk7XG4gICAgaWYgKCFjb25zdHJ1Y3RlZC5sYW5ndWFnZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdMYW5ndWFnZSBpcyByZXF1aXJlZCcpO1xuICAgIH1cbiAgfSBjYXRjaCB7XG4gICAgY29uc29sZS5lcnJvcihgQW4gaW52YWxpZCBsb2NhbGUgd2FzIHByb3ZpZGVkOiBcIiR7bG9jYWxlfVwiXFxuUGxlYXNlIGVuc3VyZSB5b3UncmUgdXNpbmcgYSB2YWxpZCBVbmljb2RlIGxvY2FsZSBpZGVudGlmaWVyIChlLmcuIFwiZW4tVVNcIikuYCk7XG4gIH1cbn1cblxuZXhwb3J0IHsgdmFsaWRhdGVMb2NhbGUgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\afersata\\\\lawblog\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\development\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\afersata\\lawblog\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ HEADER_LOCALE_NAME)\n/* harmony export */ });\n// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEtyYWltYXRpY1xcRGVza3RvcFxcYWZlcnNhdGFcXGxhd2Jsb2dcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzaGFyZWRcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBVc2VkIHRvIHJlYWQgdGhlIGxvY2FsZSBmcm9tIHRoZSBtaWRkbGV3YXJlXG5jb25zdCBIRUFERVJfTE9DQUxFX05BTUUgPSAnWC1ORVhULUlOVEwtTE9DQUxFJztcblxuZXhwb3J0IHsgSEVBREVSX0xPQ0FMRV9OQU1FIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider({ locale, ...rest }) {\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFDOEM7QUFDTjtBQUV4QyxTQUFTRSx1QkFBdUIsRUFDOUJDLE1BQU0sRUFDTixHQUFHQyxNQUNKO0lBQ0MsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtBQUU2QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxLcmFpbWF0aWNcXERlc2t0b3BcXGFmZXJzYXRhXFxsYXdibG9nXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2hhcmVkXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgSW50bFByb3ZpZGVyIH0gZnJvbSAndXNlLWludGwvcmVhY3QnO1xuaW1wb3J0IHsganN4IH0gZnJvbSAncmVhY3QvanN4LXJ1bnRpbWUnO1xuXG5mdW5jdGlvbiBOZXh0SW50bENsaWVudFByb3ZpZGVyKHtcbiAgbG9jYWxlLFxuICAuLi5yZXN0XG59KSB7XG4gIGlmICghbG9jYWxlKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ291bGRuJ3QgaW5mZXIgdGhlIGBsb2NhbGVgIHByb3AgaW4gYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgLCBwbGVhc2UgcHJvdmlkZSBpdCBleHBsaWNpdGx5LlxcblxcblNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI2xvY2FsZVwiICk7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9qc3goSW50bFByb3ZpZGVyLCB7XG4gICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgLi4ucmVzdFxuICB9KTtcbn1cblxuZXhwb3J0IHsgTmV4dEludGxDbGllbnRQcm92aWRlciBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOlsiSW50bFByb3ZpZGVyIiwianN4IiwiTmV4dEludGxDbGllbnRQcm92aWRlciIsImxvY2FsZSIsInJlc3QiLCJFcnJvciIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n");

/***/ })

};
;