'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { BookOpen } from 'lucide-react'

export function SimpleNavigation() {
  const params = useParams()
  const locale = params.locale as string

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              አፈሳታ
            </span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href={`/${locale}`} className="text-gray-700 hover:text-blue-600 transition-colors">
              Home
            </Link>
            <Link href={`/${locale}/articles`} className="text-gray-700 hover:text-blue-600 transition-colors">
              Articles
            </Link>
            <Link href={`/${locale}/categories`} className="text-gray-700 hover:text-blue-600 transition-colors">
              Categories
            </Link>
          </div>

          {/* Language Switcher */}
          <div className="flex items-center space-x-4">
            <Link
              href="/en"
              className={`px-2 py-1 text-sm rounded ${locale === 'en' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            >
              EN
            </Link>
            <Link
              href="/am"
              className={`px-2 py-1 text-sm rounded ${locale === 'am' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-blue-600'}`}
            >
              አማ
            </Link>
          </div>
        </div>
      </div>
    </nav>
  )
}
