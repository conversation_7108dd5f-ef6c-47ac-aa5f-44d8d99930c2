'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { Clock, User, ArrowRight, Heart, MessageCircle, Bookmark } from 'lucide-react'
import { formatDate, formatRelativeTime } from '@/lib/utils'

// Mock data - in real app, this would come from Supabase
const featuredPosts = [
  {
    id: '1',
    title: 'The Future of Constitutional Law in the Digital Age',
    excerpt: 'Exploring how constitutional principles adapt to emerging technologies and digital rights in the 21st century.',
    content: '',
    slug: 'future-constitutional-law-digital-age',
    featured_image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=800&h=600&fit=crop',
    author: {
      id: '1',
      full_name: '<PERSON>',
      avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      university: 'Harvard Law School'
    },
    category: {
      id: '1',
      name: 'Constitutional Law',
      slug: 'constitutional-law',
      color: '#DC2626'
    },
    reading_time: 8,
    view_count: 1250,
    like_count: 89,
    published_at: '2024-01-15T10:00:00Z',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Breaking Down the Latest Supreme Court Decision',
    excerpt: 'A comprehensive analysis of the recent landmark ruling and its implications for future legal precedent.',
    content: '',
    slug: 'supreme-court-decision-analysis',
    featured_image: 'https://images.unsplash.com/photo-1589994965851-a8f479c573a9?w=800&h=600&fit=crop',
    author: {
      id: '2',
      full_name: 'Marcus Johnson',
      avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      university: 'Yale Law School'
    },
    category: {
      id: '2',
      name: 'Criminal Law',
      slug: 'criminal-law',
      color: '#7C2D12'
    },
    reading_time: 12,
    view_count: 2100,
    like_count: 156,
    published_at: '2024-01-12T14:30:00Z',
    created_at: '2024-01-12T14:30:00Z'
  },
  {
    id: '3',
    title: 'Environmental Law and Climate Change: A Student\'s Perspective',
    excerpt: 'How the next generation of lawyers is approaching environmental challenges and climate litigation.',
    content: '',
    slug: 'environmental-law-climate-change-perspective',
    featured_image: 'https://images.unsplash.com/photo-1569163139394-de44cb5894c6?w=800&h=600&fit=crop',
    author: {
      id: '3',
      full_name: 'Emma Rodriguez',
      avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      university: 'Stanford Law School'
    },
    category: {
      id: '3',
      name: 'Environmental Law',
      slug: 'environmental-law',
      color: '#059669'
    },
    reading_time: 6,
    view_count: 890,
    like_count: 67,
    published_at: '2024-01-10T09:15:00Z',
    created_at: '2024-01-10T09:15:00Z'
  }
]

export function FeaturedPosts() {
  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold font-playfair mb-6">
            Featured <span className="gradient-text">Articles</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Discover the most insightful and thought-provoking articles from our community of legal minds.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {featuredPosts.map((post, index) => (
            <motion.article
              key={post.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group glass rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]"
            >
              {/* Featured Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={post.featured_image}
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                
                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <span
                    className="px-3 py-1 text-xs font-semibold text-white rounded-full"
                    style={{ backgroundColor: post.category.color }}
                  >
                    {post.category.name}
                  </span>
                </div>

                {/* Bookmark Button */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                >
                  <Bookmark className="w-4 h-4" />
                </motion.button>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Author Info */}
                <div className="flex items-center space-x-3 mb-4">
                  <Image
                    src={post.author.avatar_url}
                    alt={post.author.full_name}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white text-sm">
                      {post.author.full_name}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400 text-xs">
                      {post.author.university}
                    </p>
                  </div>
                </div>

                {/* Title */}
                <Link href={`/articles/${post.slug}`}>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                </Link>

                {/* Excerpt */}
                <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{post.reading_time} min read</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{post.view_count.toLocaleString()} views</span>
                    </div>
                  </div>
                  <span>{formatRelativeTime(post.published_at)}</span>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="flex items-center space-x-1 text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <Heart className="w-4 h-4" />
                      <span className="text-sm">{post.like_count}</span>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="flex items-center space-x-1 text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span className="text-sm">12</span>
                    </motion.button>
                  </div>

                  <Link
                    href={`/articles/${post.slug}`}
                    className="inline-flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm group"
                  >
                    <span>Read More</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </motion.article>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Link
            href="/articles"
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span>View All Articles</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
