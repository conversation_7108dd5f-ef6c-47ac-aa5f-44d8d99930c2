"use client";import{useFocusRing as Y}from"@react-aria/focus";import{useHover as Z}from"@react-aria/interactions";import G,{createContext as ee,useCallback as be,useContext as te,useMemo as x,useReducer as ge,useRef as B}from"react";import{useByComparator as Oe}from'../../hooks/use-by-comparator.js';import{useControllable as Pe}from'../../hooks/use-controllable.js';import{useDefaultValue as ve}from'../../hooks/use-default-value.js';import{useEvent as H}from'../../hooks/use-event.js';import{useId as V}from'../../hooks/use-id.js';import{useIsoMorphicEffect as oe}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as re}from'../../hooks/use-latest-value.js';import{useSyncRefs as K}from'../../hooks/use-sync-refs.js';import{useDisabled as ne}from'../../internal/disabled.js';import{FormFields as De}from'../../internal/form-fields.js';import{useProvidedId as Ae}from'../../internal/id.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{Focus as N,FocusResult as ae,focusIn as pe,sortByDomNode as _e}from'../../utils/focus-management.js';import{attemptSubmit as Ee}from'../../utils/form.js';import{match as Ge}from'../../utils/match.js';import{getOwnerDocument as xe}from'../../utils/owner.js';import{forwardRefWithAs as $,mergeProps as le,useRender as j}from'../../utils/render.js';import{Description as Ce,useDescribedBy as he,useDescriptions as se}from'../description/description.js';import{Keys as U}from'../keyboard.js';import{Label as Le,useLabelledBy as ke,useLabels as de}from'../label/label.js';var Ie=(e=>(e[e.RegisterOption=0]="RegisterOption",e[e.UnregisterOption=1]="UnregisterOption",e))(Ie||{});let Fe={[0](o,t){let e=[...o.options,{id:t.id,element:t.element,propsRef:t.propsRef}];return{...o,options:_e(e,i=>i.element.current)}},[1](o,t){let e=o.options.slice(),i=o.options.findIndex(v=>v.id===t.id);return i===-1?o:(e.splice(i,1),{...o,options:e})}},J=ee(null);J.displayName="RadioGroupDataContext";function X(o){let t=te(J);if(t===null){let e=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,X),e}return t}let z=ee(null);z.displayName="RadioGroupActionsContext";function q(o){let t=te(z);if(t===null){let e=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,q),e}return t}function Ue(o,t){return Ge(t.type,Fe,o,t)}let Me="div";function Se(o,t){let e=V(),i=ne(),{id:v=`headlessui-radiogroup-${e}`,value:m,form:D,name:n,onChange:f,by:u,disabled:a=i||!1,defaultValue:M,tabIndex:T=0,...S}=o,R=Oe(u),[A,y]=ge(Ue,{options:[]}),p=A.options,[C,_]=de(),[h,L]=se(),k=B(null),c=K(k,t),b=ve(M),[l,I]=Pe(m,f,b),g=x(()=>p.find(r=>!r.propsRef.current.disabled),[p]),O=x(()=>p.some(r=>R(r.propsRef.current.value,l)),[p,l]),s=H(r=>{var d;if(a||R(r,l))return!1;let F=(d=p.find(w=>R(w.propsRef.current.value,r)))==null?void 0:d.propsRef.current;return F!=null&&F.disabled?!1:(I==null||I(r),!0)}),ue=H(r=>{let F=k.current;if(!F)return;let d=xe(F),w=p.filter(P=>P.propsRef.current.disabled===!1).map(P=>P.element.current);switch(r.key){case U.Enter:Ee(r.currentTarget);break;case U.ArrowLeft:case U.ArrowUp:if(r.preventDefault(),r.stopPropagation(),pe(w,N.Previous|N.WrapAround)===ae.Success){let E=p.find(W=>W.element.current===(d==null?void 0:d.activeElement));E&&s(E.propsRef.current.value)}break;case U.ArrowRight:case U.ArrowDown:if(r.preventDefault(),r.stopPropagation(),pe(w,N.Next|N.WrapAround)===ae.Success){let E=p.find(W=>W.element.current===(d==null?void 0:d.activeElement));E&&s(E.propsRef.current.value)}break;case U.Space:{r.preventDefault(),r.stopPropagation();let P=p.find(E=>E.element.current===(d==null?void 0:d.activeElement));P&&s(P.propsRef.current.value)}break}}),Q=H(r=>(y({type:0,...r}),()=>y({type:1,id:r.id}))),ce=x(()=>({value:l,firstOption:g,containsCheckedOption:O,disabled:a,compare:R,tabIndex:T,...A}),[l,g,O,a,R,T,A]),fe=x(()=>({registerOption:Q,change:s}),[Q,s]),Te={ref:c,id:v,role:"radiogroup","aria-labelledby":C,"aria-describedby":h,onKeyDown:ue},Re=x(()=>({value:l}),[l]),me=be(()=>{if(b!==void 0)return s(b)},[s,b]),ye=j();return G.createElement(L,{name:"RadioGroup.Description"},G.createElement(_,{name:"RadioGroup.Label"},G.createElement(z.Provider,{value:fe},G.createElement(J.Provider,{value:ce},n!=null&&G.createElement(De,{disabled:a,data:{[n]:l||"on"},overrides:{type:"radio",checked:l!=null},form:D,onReset:me}),ye({ourProps:Te,theirProps:S,slot:Re,defaultTag:Me,name:"RadioGroup"})))))}let He="div";function we(o,t){var g;let e=X("RadioGroup.Option"),i=q("RadioGroup.Option"),v=V(),{id:m=`headlessui-radiogroup-option-${v}`,value:D,disabled:n=e.disabled||!1,autoFocus:f=!1,...u}=o,a=B(null),M=K(a,t),[T,S]=de(),[R,A]=se(),y=re({value:D,disabled:n});oe(()=>i.registerOption({id:m,element:a,propsRef:y}),[m,i,a,y]);let p=H(O=>{var s;if(ie(O.currentTarget))return O.preventDefault();i.change(D)&&((s=a.current)==null||s.focus())}),C=((g=e.firstOption)==null?void 0:g.id)===m,{isFocusVisible:_,focusProps:h}=Y({autoFocus:f}),{isHovered:L,hoverProps:k}=Z({isDisabled:n}),c=e.compare(e.value,D),b=le({ref:M,id:m,role:"radio","aria-checked":c?"true":"false","aria-labelledby":T,"aria-describedby":R,"aria-disabled":n?!0:void 0,tabIndex:(()=>n?-1:c||!e.containsCheckedOption&&C?e.tabIndex:-1)(),onClick:n?void 0:p,autoFocus:f},h,k),l=x(()=>({checked:c,disabled:n,active:_,hover:L,focus:_,autofocus:f}),[c,n,L,_,f]),I=j();return G.createElement(A,{name:"RadioGroup.Description"},G.createElement(S,{name:"RadioGroup.Label"},I({ourProps:b,theirProps:u,slot:l,defaultTag:He,name:"RadioGroup.Option"})))}let Ne="span";function We(o,t){var g;let e=X("Radio"),i=q("Radio"),v=V(),m=Ae(),D=ne(),{id:n=m||`headlessui-radio-${v}`,value:f,disabled:u=e.disabled||D||!1,autoFocus:a=!1,...M}=o,T=B(null),S=K(T,t),R=ke(),A=he(),y=re({value:f,disabled:u});oe(()=>i.registerOption({id:n,element:T,propsRef:y}),[n,i,T,y]);let p=H(O=>{var s;if(ie(O.currentTarget))return O.preventDefault();i.change(f)&&((s=T.current)==null||s.focus())}),{isFocusVisible:C,focusProps:_}=Y({autoFocus:a}),{isHovered:h,hoverProps:L}=Z({isDisabled:u}),k=((g=e.firstOption)==null?void 0:g.id)===n,c=e.compare(e.value,f),b=le({ref:S,id:n,role:"radio","aria-checked":c?"true":"false","aria-labelledby":R,"aria-describedby":A,"aria-disabled":u?!0:void 0,tabIndex:(()=>u?-1:c||!e.containsCheckedOption&&k?e.tabIndex:-1)(),autoFocus:a,onClick:u?void 0:p},_,L),l=x(()=>({checked:c,disabled:u,hover:h,focus:C,autofocus:a}),[c,u,h,C,a]);return j()({ourProps:b,theirProps:M,slot:l,defaultTag:Ne,name:"Radio"})}let Be=$(Se),Ve=$(we),Ke=$(We),$e=Le,je=Ce,mt=Object.assign(Be,{Option:Ve,Radio:Ke,Label:$e,Description:je});export{Ke as Radio,mt as RadioGroup,je as RadioGroupDescription,$e as RadioGroupLabel,Ve as RadioGroupOption};
